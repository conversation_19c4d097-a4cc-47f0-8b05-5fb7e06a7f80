import {Board} from './Board'
import {BoardUserStatus} from './BoardUserStatus'
import {BoardAccessType} from './BoardAccessType'
import {NotificationLevel} from './NotificationLevel'
export interface UserBoard {
  board?: Board
  status?: BoardUserStatus
  type?: BoardAccessType
  category?: number
  category_uuid?: string
  order_number?: string
  is_default?: boolean
  is_group?: boolean
  feed_unread_count?: number
  first_unread_feed_sequence?: number
  accessed_time?: number
  first_unread_feed_timestamp?: number
  enabled_time?: number
  dismissed_time?: number
  is_favorite?: boolean
  is_archive?: boolean
  archived_time?: number
  is_notification_off?: boolean
  push_notification_level?: NotificationLevel
  waiting_signatures?: number
  original_sequence?: number
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
