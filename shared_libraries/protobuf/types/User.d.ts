import {UserType} from './UserType'
import {UserOSType} from './UserOSType'
import {HashAlgorithm} from './HashAlgorithm'
import {UserToken} from './UserToken'
import {UserQRToken} from './UserQRToken'
import {UserBoard} from './UserBoard'
import {UserResource} from './UserResource'
import {UserTag} from './UserTag'
import {UserAgent} from './UserAgent'
import {UserCallLog} from './UserCallLog'
import {UserACDLog} from './UserACDLog'
import {UserRole} from './UserRole'
import {UserLevel} from './UserLevel'
import {UserCap} from './UserCap'
import {UserGroup} from './UserGroup'
import {UserWebApp} from './UserWebApp'
import {UserPartner} from './UserPartner'
import {UserBoardCategory} from './UserBoardCategory'
import {ObjectFeed} from './ObjectFeed'
import {UserContact} from './UserContact'
import {UserConnection} from './UserConnection'
import {UserRelation} from './UserRelation'
import {BotUserRelation} from './BotUserRelation'
import {NotificationLevel} from './NotificationLevel'
import {BoardMemberNotificationSetting} from './BoardMemberNotificationSetting'
import {ActionNotificationSetting} from './ActionNotificationSetting'
import {BoardNotificationSetting} from './BoardNotificationSetting'
import {UserFavorite} from './UserFavorite'
import {UserMentionMe} from './UserMentionMe'
import {UserNotification} from './UserNotification'
import {UserBroadcast} from './UserBroadcast'
import {SignatureStyle} from './SignatureStyle'
import {OutOfOfficeStatus} from './OutOfOfficeStatus'
import {UserDevice} from './UserDevice'
import {RoutingWeekday} from './RoutingWeekday'
import {RoutingSpecialDay} from './RoutingSpecialDay'
export interface User {
  id: string
  name?: string
  first_name?: string
  last_name?: string
  type?: UserType
  phone_number?: string
  online?: boolean
  in_meet?: boolean
  os_type?: UserOSType
  disabled?: boolean
  passcode_protected?: boolean
  email?: string
  unique_id?: string
  apple_device_token?: string
  agent_user_id?: string
  work_phone_number?: string
  sip_configuration?: string
  extension_phone_number?: string
  title?: string
  display_email?: string
  display_phone_number?: string
  display_id?: string
  pass?: string
  hashed_pass?: string
  pass_algorithm?: HashAlgorithm
  old_pass?: string
  address?: string
  email_onboarded?: boolean
  tokens?: string[]
  user_tokens?: UserToken[]
  qr_tokens?: UserQRToken[]
  email_verification_token?: string
  email_verification_code?: string
  code_updated_time?: number
  failed_attempts?: number
  last_login_timestamp?: number
  reset_password_timestamps?: number[]
  lookup_domain_timestamps?: number[]
  email_verified?: boolean
  token_updated_time?: number
  picture?: number
  picture2x?: number
  picture4x?: number
  picture_path?: string
  picture2x_path?: string
  picture4x_path?: string
  picture_url?: string
  android_device_token?: string
  apple_voip_token?: string
  ios_app_id?: string
  android_app_pkg_name?: string
  boards?: UserBoard[]
  personal_rooms?: UserBoard[]
  resources?: UserResource[]
  tags?: UserTag[]
  agents?: UserAgent[]
  call_logs?: UserCallLog[]
  acd_logs?: UserACDLog[]
  group_boards?: UserBoard[]
  role?: UserRole
  level?: UserLevel
  cap?: UserCap
  total_cloud_size?: number
  boards_owned?: number
  boards_invited?: number
  boards_total?: number
  boards_owned_pages?: number
  boards_owned_comments?: number
  boards_owned_todos?: number
  boards_owned_invitees?: number
  meet_hosted?: number
  meet_invited?: number
  contacts_total?: number
  relations_total?: number
  feed_unread_total?: number
  feed_unread_sr?: number
  agents_total?: number
  accessed_time?: number
  last_archive_time?: number
  last_active_time?: number
  groups?: UserGroup[]
  webapps?: UserWebApp[]
  partners?: UserPartner[]
  managed_teams?: UserGroup[]
  collab_teams?: UserGroup[]
  categories?: UserBoardCategory[]
  feeds?: ObjectFeed[]
  enable_notification_emails?: boolean
  enable_digest_email?: boolean
  last_digest_email_timestamp?: number
  timezone?: string
  language?: string
  contacts?: UserContact[]
  customized_presence_status?: number
  customized_presence_message?: string
  has_push_notification?: boolean
  collaborators?: UserContact[]
  connections?: UserConnection[]
  relations?: UserRelation[]
  bot_relations?: BotUserRelation[]
  board_notification_level?: NotificationLevel
  session_notification_level?: NotificationLevel
  board_member_notification_settings?: BoardMemberNotificationSetting
  action_notification_settings?: ActionNotificationSetting
  board_notification_settings?: BoardNotificationSetting
  favorites?: UserFavorite[]
  mentionmes?: UserMentionMe[]
  action_items?: UserBoard[]
  action_accessed_time?: number
  notifications?: UserNotification[]
  notification_accessed_time?: number
  broadcasts?: UserBroadcast[]
  ext_broadcasts?: UserBoard[]
  keep_short?: boolean
  sip_registration_status?: boolean
  sip_registration_message?: string
  sip_unread_voice_mail_count?: number
  sip_has_unread_voice_mail?: boolean
  signature?: number
  initials?: number
  initials_text?: string
  legal_name?: string
  signature_style?: SignatureStyle
  signature_path?: string
  initials_path?: string
  out_of_office?: OutOfOfficeStatus
  social?: string
  social_id?: string
  social_avatar?: string
  social_status?: string
  invitation_code?: string
  connector?: number
  social_updated_time?: number
  line_social_id?: string
  line_social_avatar?: string
  line_social_status?: string
  line_invitation_code?: string
  line_connector?: number
  whatsapp_social_id?: string
  whatsapp_social_avatar?: string
  whatsapp_social_status?: string
  whatsapp_invitation_code?: string
  whatsapp_connector?: number
  custom1?: string
  custom2?: string
  custom3?: string
  user_devices?: UserDevice[]
  flow_templates?: UserBoard[]
  weekdays?: RoutingWeekday[]
  special_days?: RoutingSpecialDay[]
  meeting_buffer_time?: number
  self_service_templates?: UserBoard[]
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  assignments?: string
  created_time?: number
  updated_time?: number
}
