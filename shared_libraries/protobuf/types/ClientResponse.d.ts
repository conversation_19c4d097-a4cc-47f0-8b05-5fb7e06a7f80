import {ClientResponseCode} from './ClientResponseCode'
import {ClientResponseDetailCode} from './ClientResponseDetailCode'
import {ClientRequestType} from './ClientRequestType'
import {CacheObject} from './CacheObject'
import {HttpHeader} from './HttpHeader'
import {ClientParam} from './ClientParam'
import {ObjectRecording} from './ObjectRecording'
import {AudioRecording} from './AudioRecording'
import {VideoRecording} from './VideoRecording'
import {DsRecording} from './DsRecording'
import {PublicViewToken} from './PublicViewToken'
import {CacheMessage} from './CacheMessage'
import {UserActivityLog} from './UserActivityLog'
import {MoxoReport} from './MoxoReport'
import {GroupCapability} from './GroupCapability'
export interface ClientResponse {
  code?: ClientResponseCode
  detail_code?: ClientResponseDetailCode
  redirect_url?: string
  sequence: string
  message?: string
  data?: string
  expires_in?: number
  timestamp?: number
  server?: string
  session_id?: string
  request_sequence?: string
  connection_id?: string
  zone?: string
  domain?: string
  is_truncated?: boolean
  marker?: string
  next_marker?: string
  type?: ClientRequestType
  object?: CacheObject
  headers?: HttpHeader[]
  params?: ClientParam[]
  hits?: number
  start?: number
  size?: number
  recordings?: ObjectRecording[]
  audios?: AudioRecording[]
  videos?: VideoRecording[]
  dss?: DsRecording[]
  token?: PublicViewToken
  client_message?: CacheMessage
  client_messages?: CacheMessage[]
  user_activities?: UserActivityLog[]
  report?: MoxoReport
  group_capability?: GroupCapability
}
