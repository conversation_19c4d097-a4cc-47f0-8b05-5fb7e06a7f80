import {BoardActor} from './BoardActor'
import {BoardSignatureStatus} from './BoardSignatureStatus'
import {DetailStatusCode} from './DetailStatusCode'
import {BoardSignee} from './BoardSignee'
import {BoardPage} from './BoardPage'
import {ObjectFeedViaSource} from './ObjectFeedViaSource'
import {DueTimeFrameType} from './DueTimeFrameType'
import {BoardReference} from './BoardReference'
import {BoardResource} from './BoardResource'
import {BoardDataReference} from './BoardDataReference'
export interface BoardSignature {
  creator?: BoardActor
  status?: BoardSignatureStatus
  detail_status?: DetailStatusCode
  signees?: BoardSignee[]
  enable_preparation?: boolean
  editor?: BoardActor
  doc_id?: string
  name?: string
  original_name?: string
  is_from_pdf_form?: boolean
  pages?: BoardPage[]
  original?: number
  coc?: number
  original_with_coc?: number
  file?: string
  description?: string
  order_number?: string
  is_template?: boolean
  template_name?: string
  template_description?: string
  original_client_uuid?: string
  sign_by_order?: boolean
  started_time?: number
  started_by_ip?: string
  started_via?: ObjectFeedViaSource
  ended_time?: number
  due_date?: number
  due_in_timeframe?: DueTimeFrameType
  exclude_weekends?: boolean
  references?: BoardReference[]
  resources?: BoardResource[]
  original_resource_sequence?: number
  pin?: number
  total_used_count?: number
  last_used_timestamp?: number
  last_modified_time?: number
  workflow?: number
  step?: number
  is_workflow_source?: boolean
  ddrs?: BoardDataReference[]
  has_custom_folder?: boolean
  custom_folder_name?: string
  sequence: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
