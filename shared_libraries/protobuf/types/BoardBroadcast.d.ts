import {UserBroadcast} from './UserBroadcast'
import {BroadcastStatus} from './BroadcastStatus'
import {BroadcastTarget} from './BroadcastTarget'
import {BroadcastChannel} from './BroadcastChannel'
export interface BoardBroadcast {
  user_list?: UserBroadcast
  status?: BroadcastStatus
  target?: BroadcastTarget
  channel?: BroadcastChannel
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  local_revision?: number
  created_time?: number
  updated_time?: number
}
