import {User} from './User'
import {BoardActor} from './BoardActor'
import {ActionUserRoster} from './ActionUserRoster'
import {RichTextFormat} from './RichTextFormat'
export interface BoardComment {
  OBSOLETE_creator_sequence?: number
  user?: User
  creator?: BoardActor
  roster?: ActionUserRoster
  text?: string
  rich_text?: string
  rich_text_format?: RichTextFormat
  x?: number
  y?: number
  position_comment_index?: number
  resource?: number
  resource_view_token?: string
  resource_path?: string
  resource_length?: number
  url_preview?: string
  timestamp?: number
  is_modified?: boolean
  is_position_comment?: boolean
  original_resource_sequence?: number
  original_comment?: number
  original_page_group?: string
  original_session?: number
  original_signature?: number
  original_transaction?: number
  original_reference_link?: number
  custom_info?: string
  social_custom_info?: string
  custom_data?: string
  pin?: number
  sequence: number
  client_uuid?: string
  revision?: number
  revisions?: BoardComment[]
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
