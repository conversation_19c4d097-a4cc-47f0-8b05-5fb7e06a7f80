import {ACDType} from './ACDType'
import {ACDStatus} from './ACDStatus'
import {User} from './User'
export interface UserACDLog {
  call_id?: string
  type?: ACDType
  status?: ACDStatus
  peer?: User
  start_time?: number
  end_time?: number
  board_id?: string
  session_key?: string
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
