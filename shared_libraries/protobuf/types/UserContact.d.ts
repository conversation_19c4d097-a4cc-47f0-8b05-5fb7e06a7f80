import {User} from './User'
import {Group} from './Group'
import {UserContactStatus} from './UserContactStatus'
export interface UserContact {
  user?: User
  group?: Group
  status?: UserContactStatus
  has_push_notification?: boolean
  is_private?: boolean
  is_from_team?: boolean
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  keep_deleted?: boolean
  created_time?: number
  updated_time?: number
}
