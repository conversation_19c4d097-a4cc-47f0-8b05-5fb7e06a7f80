import {ClientRequest} from './ClientRequest'
import {User} from './User'
import {AccessToken} from './AccessToken'
import {AsyncTaskStatus} from './AsyncTaskStatus'
import {ClientResponseDetailCode} from './ClientResponseDetailCode'
export interface AsyncTask {
  request?: ClientRequest
  input_resource?: number
  actor?: User
  token?: AccessToken
  status?: AsyncTaskStatus
  message?: string
  detail_code?: ClientResponseDetailCode
  detail_message?: string
  processed_items?: number
  total_items?: number
  sequence: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
