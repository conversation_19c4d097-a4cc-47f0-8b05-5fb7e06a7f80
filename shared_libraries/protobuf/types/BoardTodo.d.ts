import {User} from './User'
import {BoardActor} from './BoardActor'
import {DueTimeFrameType} from './DueTimeFrameType'
import {DetailStatusCode} from './DetailStatusCode'
import {BoardComment} from './BoardComment'
import {BoardResource} from './BoardResource'
import {BoardReference} from './BoardReference'
import {BoardReminder} from './BoardReminder'
import {BoardEditorType} from './BoardEditorType'
export interface BoardTodo {
  creator_sequence?: number
  OBSOLETE_user?: User
  creator?: BoardActor
  name?: string
  note?: string
  is_marked?: boolean
  assignee_sequence?: number
  assignee?: BoardActor
  due_date?: number
  due_in_timeframe?: DueTimeFrameType
  exclude_weekends?: boolean
  is_completed?: boolean
  detail_status?: DetailStatusCode
  is_template?: boolean
  template_name?: string
  template_description?: string
  original_client_uuid?: string
  total_used_count?: number
  last_used_timestamp?: number
  last_modified_time?: number
  order_number?: string
  comments?: BoardComment[]
  resources?: BoardResource[]
  references?: BoardReference[]
  reminders?: BoardReminder[]
  editable_editor_type?: BoardEditorType
  completable_editor_type?: BoardEditorType
  pin?: number
  update_if_revision_match?: number
  workflow?: number
  step?: number
  sequence: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
