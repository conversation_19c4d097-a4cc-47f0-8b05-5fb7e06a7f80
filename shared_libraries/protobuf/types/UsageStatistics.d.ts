import {GroupUsageItem} from './GroupUsageItem'
import {SessionUsageItem} from './SessionUsageItem'
import {BoardUsageItem} from './BoardUsageItem'
import {GroupUsageCount} from './GroupUsageCount'
export interface UsageStatistics {
  id: string
  group_usage_items?: GroupUsageItem[]
  session_items?: SessionUsageItem[]
  total_minutes?: number
  total_telephony_minutes?: number
  board_items?: BoardUsageItem[]
  group_counts?: GroupUsageCount
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
}
