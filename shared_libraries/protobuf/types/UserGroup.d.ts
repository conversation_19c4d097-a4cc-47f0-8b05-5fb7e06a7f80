import {Group} from './Group'
import {GroupUserStatus} from './GroupUserStatus'
import {GroupAccessType} from './GroupAccessType'
export interface UserGroup {
  group?: Group
  status?: GroupUserStatus
  type?: GroupAccessType
  role?: number
  roles?: number[]
  onboarded_time?: number
  group_sequence?: number
  member_alias?: string
  order_number?: string
  tac_agreed_time?: number
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
