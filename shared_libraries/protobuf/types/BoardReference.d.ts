import {Board} from './Board'
import {User} from './User'
import {BoardActor} from './BoardActor'
import {BoardReferenceType} from './BoardReferenceType'
export interface BoardReference {
  board?: Board
  creator_sequence?: number
  OBSOLETE_user?: User
  creator?: BoardActor
  type?: BoardReferenceType
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  custom_data?: string
  keep_deleted?: boolean
  created_time?: number
  updated_time?: number
}
