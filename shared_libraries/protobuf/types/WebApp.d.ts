import {WebAppType} from './WebAppType'
import {WebAppUser} from './WebAppUser'
import {UserTag} from './UserTag'
import {UserResource} from './UserResource'
import {NotificationVendor} from './NotificationVendor'
export interface WebApp {
  id: string
  type?: WebAppType
  app_name?: string
  description?: string
  description_long?: string
  redirect_url?: string
  client_id?: string
  client_secret?: string
  grant_types?: string[]
  scopes?: string[]
  picture?: number
  category?: string
  verification_token?: string
  verification_url?: string
  callback_url?: string
  template?: string
  template_richtext?: string
  instructions?: string
  note?: string
  desktop_home_url?: string
  mobile_home_url?: string
  status?: string
  nlp_type?: string
  apns_cert?: string
  apns_private_key?: string
  apns_password?: string
  is_apns_cert_expired?: boolean
  apns_development?: boolean
  voip_cert?: string
  voip_private_key?: string
  voip_password?: string
  is_voip_cert_expired?: boolean
  gcm_api_key?: string
  optional_gcm_url?: string
  has_badge?: boolean
  ios_app_id?: string
  auth_key_id?: string
  auth_key?: string
  team_id?: string
  bundle_id?: string
  apple_oauth_bundle_id?: string
  android_app_namespace?: string
  android_app_pkg_name?: string
  android_app_fingerprints?: string[]
  google_oauth_client_id?: string
  owner?: WebAppUser
  sound_default?: string
  sound_meeting_call?: string
  tags?: UserTag[]
  resources?: UserResource[]
  vendors?: NotificationVendor[]
  is_universal?: boolean
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  assignments?: string
  created_time?: number
  updated_time?: number
}
