import {BoardActor} from './BoardActor'
import {ClientResponseCode} from './ClientResponseCode'
import {WorkflowStatus} from './WorkflowStatus'
import {WorkflowObject} from './WorkflowObject'
import {WorkflowType} from './WorkflowType'
import {WorkflowVar} from './WorkflowVar'
import {WorkflowStep} from './WorkflowStep'
import {WorkflowMilestone} from './WorkflowMilestone'
import {WorkflowOutgoing} from './WorkflowOutgoing'
import {WorkflowTriggerType} from './WorkflowTriggerType'
import {WorkflowTriggerError} from './WorkflowTriggerError'
import {Board} from './Board'
export interface BoardWorkflow {
  creator?: BoardActor
  user?: BoardActor
  template_board_id?: string
  original_template_board_id?: string
  update_from_original?: boolean
  template_name?: string
  name?: string
  description?: string
  welcome_msg?: string
  welcome_msg_comment_sequence?: number
  error_msg?: string
  error_code?: ClientResponseCode
  status?: WorkflowStatus
  definition?: string
  nodes?: string
  objects?: WorkflowObject[]
  processed_steps?: number
  total_steps?: number
  current_step?: number
  is_template?: boolean
  is_active?: boolean
  process_in_parallel?: boolean
  type?: WorkflowType
  variables?: WorkflowVar[]
  steps?: WorkflowStep[]
  milestones?: WorkflowMilestone[]
  outgoings?: WorkflowOutgoing[]
  total_used_count?: number
  last_used_timestamp?: number
  last_modified_time?: number
  completed_time?: number
  original_signature?: number
  original_transaction?: number
  reference_id?: string
  trigger_type?: WorkflowTriggerType
  trigger_activation_time?: number
  trigger_error?: WorkflowTriggerError
  sequence: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  internal_board?: Board
  created_time?: number
  updated_time?: number
}
