import {BoardResourceType} from './BoardResourceType'
import {User} from './User'
import {BoardActor} from './BoardActor'
import {BoardResourceStatus} from './BoardResourceStatus'
import {BoardPage} from './BoardPage'
import {ActionObject} from './ActionObject'
import {Board} from './Board'
export interface BoardResource {
  part?: string
  origin?: string
  use_origin_grouping?: boolean
  name?: string
  path?: string
  type?: BoardResourceType
  content_type?: string
  content_length?: number
  media_length?: number
  is_password_protected?: boolean
  password?: string
  sha256_hash?: string
  hash?: string
  rotate?: number
  width?: number
  height?: number
  OBSOLETE_creator_sequence?: number
  OBSOLETE_creator?: User
  creator?: BoardActor
  upload_sequence?: string
  status?: BoardResourceStatus
  converted_pages?: number
  total_pages?: number
  max_pages?: number
  pages?: BoardPage[]
  session?: ActionObject
  email_subject?: string
  from_email?: string
  from_name?: string
  is_email_empty?: boolean
  file?: string
  destination_board?: Board
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  original_resource_sequence?: number
  created_time?: number
  updated_time?: number
}
