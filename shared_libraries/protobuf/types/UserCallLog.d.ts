import {CallType} from './CallType'
import {CallStatus} from './CallStatus'
import {CallUser} from './CallUser'
import {ClientType} from './ClientType'
export interface UserCallLog {
  call_id?: string
  type?: CallType
  status?: CallStatus
  peer?: CallUser
  start_time?: number
  end_time?: number
  board_id?: string
  session_key?: string
  session_id?: string
  client_type?: ClientType
  sip_call_id?: string
  dtmf_digits?: string
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
