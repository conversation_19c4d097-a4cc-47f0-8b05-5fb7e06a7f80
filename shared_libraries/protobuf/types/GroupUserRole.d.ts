import {GroupUserRoleType} from './GroupUserRoleType'
import {GroupRoleCategory} from './GroupRoleCategory'
import {ChatPrivilege} from './ChatPrivilege'
import {MeetPrivilege} from './MeetPrivilege'
import {RelationPrivilege} from './RelationPrivilege'
import {FilePrivilege} from './FilePrivilege'
import {RoutingPrivilege} from './RoutingPrivilege'
import {GroupPrivilege} from './GroupPrivilege'
import {ContactPrivilege} from './ContactPrivilege'
export interface GroupUserRole {
  name?: string
  is_default?: boolean
  type?: GroupUserRoleType
  description?: string
  include_all_admins?: boolean
  include_all_internal_users?: boolean
  category?: GroupRoleCategory
  chat?: ChatPrivilege
  meet?: MeetPrivilege
  relation?: RelationPrivilege
  file?: FilePrivilege
  routing?: RoutingPrivilege
  audit?: GroupPrivilege
  contact?: ContactPrivilege
  role_template?: string
  users_total?: number
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
