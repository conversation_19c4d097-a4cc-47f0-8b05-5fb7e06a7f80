import {GroupType} from './GroupType'
import {GroupContact} from './GroupContact'
import {GroupCap} from './GroupCap'
import {GroupSetting} from './GroupSetting'
import {GroupSupport} from './GroupSupport'
import {GroupUser} from './GroupUser'
import {GroupUserSetting} from './GroupUserSetting'
import {GroupSubscriptionStatus} from './GroupSubscriptionStatus'
import {GroupPartner} from './GroupPartner'
import {GroupBoard} from './GroupBoard'
import {GroupIntegration} from './GroupIntegration'
import {UserGroup} from './UserGroup'
import {UserCap} from './UserCap'
import {UserResource} from './UserResource'
import {UserTag} from './UserTag'
import {GroupTelephonyDomain} from './GroupTelephonyDomain'
import {GroupUserRole} from './GroupUserRole'
import {AsyncTask} from './AsyncTask'
import {GroupAppConfig} from './GroupAppConfig'
import {RoutingConfig} from './RoutingConfig'
import {UserQRToken} from './UserQRToken'
import {Property} from './Property'
export interface Group {
  id: string
  name?: string
  type?: GroupType
  alias?: string
  description?: string
  timezone?: string
  contact?: GroupContact
  group_caps?: GroupCap
  group_settings?: GroupSetting
  support?: GroupSupport
  client_support?: GroupSupport
  plan_code?: string
  plan_code_local?: string
  plan_quantity?: number
  plan_quantity_local?: number
  members?: GroupUser[]
  managers?: GroupUser[]
  managers_setting?: GroupUserSetting
  status?: GroupSubscriptionStatus
  template_name?: string
  customer_id?: string
  coupon_id?: string
  cancel_subscription_at_period_end?: boolean
  scheduled_plan_code?: string
  trial_start_time?: number
  trial_end_time?: number
  commitment_end_time?: number
  cancellation_request_time?: number
  partner?: GroupPartner
  picture?: number
  tac?: number
  web_version?: string
  board_owner_privileges?: number
  board_editor_privileges?: number
  board_viewer_privileges?: number
  boards?: GroupBoard[]
  recurly_signature?: string
  integrations?: GroupIntegration[]
  teams?: UserGroup[]
  cap?: UserCap
  resources?: UserResource[]
  tags?: UserTag[]
  group_telephony_domain?: GroupTelephonyDomain
  roles?: GroupUserRole[]
  tasks?: AsyncTask[]
  app_config?: GroupAppConfig
  routing_config?: RoutingConfig
  invitation_tokens?: UserQRToken[]
  redeem_urls?: UserTag[]
  redeem_url_idx?: number
  board_properties?: Property[]
  shared_content_library_group_id?: string
  total_members?: number
  total_local_members?: number
  total_content_libraries?: number
  total_managers?: number
  istemp?: boolean
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  assignments?: string
  created_time?: number
  updated_time?: number
}
