import {User} from './User'
import {Group} from './Group'
import {AudioStatus} from './AudioStatus'
import {AudioStatusRequest} from './AudioStatusRequest'
import {BoardWaitingUserStatus} from './BoardWaitingUserStatus'
import {TelephoneStatus} from './TelephoneStatus'
import {RosterVideoStatus} from './RosterVideoStatus'
export interface ActionUserRoster {
  id: string
  requestor_id?: string
  user?: User
  group?: Group
  audio_status?: AudioStatus
  audio_status_request?: AudioStatusRequest
  waiting_user_status?: BoardWaitingUserStatus
  is_host?: boolean
  is_presenter?: boolean
  is_invisible?: boolean
  is_from_team?: boolean
  participant_number?: number
  telephone_status?: TelephoneStatus
  video_status?: RosterVideoStatus
  roster_tag?: string
  is_deleted?: boolean
  keep_deleted?: boolean
  created_time?: number
  updated_time?: number
  revision?: number
  sequence: number
  client_uuid?: string
  assignments?: string
}
