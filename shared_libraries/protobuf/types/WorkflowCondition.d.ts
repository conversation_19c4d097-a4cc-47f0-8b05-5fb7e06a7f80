import {WorkflowConditionCategory} from './WorkflowConditionCategory'
import {WorkflowVar} from './WorkflowVar'
export interface WorkflowCondition {
  name?: string
  expression?: string
  result?: string
  original_result?: string
  resolved?: boolean
  category?: WorkflowConditionCategory
  conditional_step?: string
  waiting_steps?: string[]
  waiting_milestones?: string[]
  waiting_conditional_steps?: string[]
  variables?: WorkflowVar[]
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  local_revision?: number
  created_time?: number
  updated_time?: number
}
