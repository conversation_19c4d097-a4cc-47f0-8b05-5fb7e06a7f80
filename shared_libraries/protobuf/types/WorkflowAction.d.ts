import {WorkflowActionStatus} from './WorkflowActionStatus'
import {WorkflowActionType} from './WorkflowActionType'
import {BoardReference} from './BoardReference'
export interface WorkflowAction {
  name?: string
  status?: WorkflowActionStatus
  type?: WorkflowActionType
  board_id?: string
  board_view_token?: string
  input?: BoardReference
  destination_board_id?: string
  output?: BoardReference
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
