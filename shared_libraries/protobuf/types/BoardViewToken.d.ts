import {BoardAccessType} from './BoardAccessType'
import {BoardUser} from './BoardUser'
import {User} from './User'
import {BoardActor} from './BoardActor'
import {BoardPage} from './BoardPage'
import {BoardResource} from './BoardResource'
import {BoardPageGroup} from './BoardPageGroup'
import {BoardFolder} from './BoardFolder'
import {BoardSignature} from './BoardSignature'
import {WorkflowVar} from './WorkflowVar'
export interface BoardViewToken {
  token?: string
  expire_timestamp?: number
  type?: BoardAccessType
  actor?: BoardUser
  actor_file_as?: User
  creator?: BoardActor
  pages?: BoardPage[]
  resources?: BoardResource[]
  page_groups?: BoardPageGroup[]
  folders?: BoardFolder[]
  signatures?: BoardSignature[]
  is_invitation_token?: boolean
  note?: string
  is_outgoing?: boolean
  disabled?: boolean
  users?: BoardUser[]
  variables?: WorkflowVar[]
  is_share_token?: boolean
  code?: string
  auto_approve?: boolean
  member_only?: boolean
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
