import {BoardActor} from './BoardActor'
import {BoardSigneeStatus} from './BoardSigneeStatus'
import {ObjectFeedViaSource} from './ObjectFeedViaSource'
import {SignatureStyle} from './SignatureStyle'
export interface BoardSignee {
  elements?: number[]
  submitted_elements?: number[]
  actor?: BoardActor
  is_submitted?: boolean
  status?: BoardSigneeStatus
  msg?: string
  order_number?: string
  requested_time?: number
  viewed_time?: number
  submitted_time?: number
  submitted_ip?: string
  submitted_via?: ObjectFeedViaSource
  signature?: number
  initials_text?: string
  signature_style?: SignatureStyle
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
