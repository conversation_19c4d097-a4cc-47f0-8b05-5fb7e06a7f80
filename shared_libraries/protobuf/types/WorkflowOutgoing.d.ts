import {ObjectFeed} from './ObjectFeed'
import {WorkflowOutgoingQueueType} from './WorkflowOutgoingQueueType'
export interface WorkflowOutgoing {
  name?: string
  feed?: ObjectFeed
  payload?: string
  webhooks?: string
  triggers?: string
  integrations?: string
  integrations_ext?: string
  queue_type?: WorkflowOutgoingQueueType
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
