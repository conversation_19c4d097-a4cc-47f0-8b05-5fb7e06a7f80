import {User} from './User'
import {BoardActor} from './BoardActor'
import {BoardPageElement} from './BoardPageElement'
import {BoardResource} from './BoardResource'
import {BoardTag} from './BoardTag'
import {BoardPageType} from './BoardPageType'
import {BoardComment} from './BoardComment'
import {BoardEditorType} from './BoardEditorType'
import {BoardPageFormField} from './BoardPageFormField'
import {BoardDataReference} from './BoardDataReference'
export interface BoardPage {
  creator_sequence?: number
  OBSOLETE_user?: User
  creator?: BoardActor
  name?: string
  svg_begining_tag?: string
  contents?: BoardPageElement[]
  svg_ending_tag?: string
  original_resource_name?: string
  original_page_number?: number
  original_resource_upload_sequence?: string
  original_resource_sequence?: number
  inherited_original_resource_sequence?: number
  is_original_resource_from_page?: boolean
  update_if_revision_match?: number
  resources?: BoardResource[]
  longitude?: string
  latitude?: string
  tags?: BoardTag[]
  page_number?: string
  page_group?: string
  file?: string
  page_type?: BoardPageType
  original?: number
  vector?: number
  background?: number
  thumbnail?: number
  text?: number
  thumbnail_view_token?: string
  media_length?: number
  original_path?: string
  vector_path?: string
  background_path?: string
  thumbnail_path?: string
  width?: number
  height?: number
  url?: string
  rotate?: number
  comments?: BoardComment[]
  total_comments?: number
  total_position_comments?: number
  original_session_key?: string
  editor?: number
  editor_actor?: BoardActor
  editor_time?: number
  editor_type?: BoardEditorType
  description?: string
  vector_thumbnail?: number
  vector_thumbnail_path?: string
  form_fields?: BoardPageFormField[]
  card?: string
  ddrs?: BoardDataReference[]
  sequence: number
  client_uuid?: string
  revision?: number
  local_revision?: number
  is_deleted?: boolean
  assignments?: string
  created_time?: number
  updated_time?: number
}
