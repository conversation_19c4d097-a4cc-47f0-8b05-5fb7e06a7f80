import {BoardActor} from './BoardActor'
import {WorkflowVarParam} from './WorkflowVarParam'
import {WorkflowVarType} from './WorkflowVarType'
export interface WorkflowVar {
  name?: string
  string_value?: string
  default_value?: string
  default_actor?: BoardActor
  label?: string
  refer_to?: string
  resolved?: boolean
  type?: string
  custom_data?: string
  params?: WorkflowVarParam[]
  var_type?: WorkflowVarType
  step_uuid?: string
  timeout?: number
  sequence: number
  client_uuid?: string
  revision?: number
  is_deleted?: boolean
  created_time?: number
  updated_time?: number
}
