import { ClientRequest, ClientResponse, ClientResponseCode } from '@moxo/proto';
import {
  AjaxContentType,
  AjaxMethod,
  IRequestParam,
  IRequestPromise,
  RequestPromise,
  sendRequest,
} from '../network/ajax';
import sdkConfig from './config';
import { BizServerError, convertToBizServerError } from './bizServerError';

export const SUCCESS_RESPONSE_CODES = [
  ClientResponseCode.RESPONSE_SUCCESS,
  ClientResponseCode.RESPONSE_ACCEPTED,
  ClientResponseCode.RESPONSE_NO_CONTENT,
  ClientResponseCode.RESPONSE_CONNECT_SUCCESS,
] as const;

/**
 * 成功响应状态码类型
 */
export type SuccessResponseCode = (typeof SUCCESS_RESPONSE_CODES)[number];

export function isSuccessResponse(response: ClientResponse): boolean {
  if (!response.code) {
    return true;
  }
  return SUCCESS_RESPONSE_CODES.includes(response.code as SuccessResponseCode);
}

/**
 * 业务请求 Promise 类，继承自 RequestPromise
 * 成功时返回 ClientResponse 对象，失败时返回 BizServerError 对象
 *
 * @example
 * ```typescript
 * const request = sendBizServerRequest(clientRequest, { method: AjaxMethod.POST });
 *
 * // 使用 async/await
 * try {
 *   const response = await request;
 *   console.log('成功:', response);
 * } catch (error) {
 *   if (error instanceof BizServerError) {
 *     console.error('业务错误:', error.message, error.code);
 *   }
 * }
 *
 * // 使用 Promise 链式调用
 * request
 *   .then(response => console.log('成功:', response))
 *   .catch(error => console.error('失败:', error));
 * ```
 */
export class RequestPromise<ClientResponse> implements IRequestPromise {
  constructor(
    executor: (
      resolve: (value: ClientResponse | PromiseLike<ClientResponse>) => void,
      reject: (reason?: BizServerError) => void,
    ) => void,
    requestId: string,
  ) {
    super(executor, requestId);
  }

  /**
   * 从普通的 RequestPromise 创建 IRequestPromise
   * 自动处理响应转换和错误处理
   *
   * @param requestPromise 原始请求 Promise，必须包含 requestId 属性
   * @returns 返回 IRequestPromise 实例
   */
  static fromRequest(requestPromise: IRequestPromise<unknown>): IRequestPromise<ClientResponse> {
    return new RequestPromise((resolve, reject) => {
      requestPromise
        .then((response) => {
          const clientResponse = response as ClientResponse;

          // 检查响应是否表示成功
          const isSuccess = isSuccessResponse(clientResponse);

          if (!isSuccess) {
            reject(convertToBizServerError(clientResponse));
          } else {
            resolve(clientResponse);
          }
        })
        .catch((error) => {
          reject(convertToBizServerError(error));
        });
    }, requestPromise.requestId);
  }

  /**
   * 创建一个立即解析的 IRequestPromise
   * @param response 响应数据
   * @param requestId 请求ID
   * @returns 立即解析的 IRequestPromise
   */
  static createResolved(response: ClientResponse): IRequestPromise<ClientResponse> {
    return new RequestPromise((resolve) => {
      resolve(response);
    }, '');
  }

  /**
   * 创建一个立即拒绝的 IRequestPromise
   * @param error 错误对象
   * @param requestId 请求ID
   * @returns 立即拒绝的 IRequestPromise
   */
  static createRejected(error: BizServerError): RequestPromise<BizServerError> {
    return new RequestPromise((_, reject) => {
      reject(error);
    }, '');
  }
}

export function sendBizServerRequest(
  body: ClientRequest,
  opts: IRequestParam,
): RequestPromise<
  { success: true; data: ClientResponse } | { success: false; error: BizServerError }
> {
  let requestUrl = window.location.origin + sdkConfig.servicePath;
  if (body.object?.board) {
    requestUrl += '/board';
  } else if (body.object?.user) {
    requestUrl += '/user';
  } else if (body.object?.group) {
    requestUrl += '/group';
  }
  const opt = { ...opts };
  if (!opt.method) {
    opt.method = AjaxMethod.POST;
  }
  if (!opt.contentType) {
    opt.contentType = AjaxContentType.JSON;
  }
  return RequestPromise.fromRequest(sendRequest(requestUrl, opt, body));
}
