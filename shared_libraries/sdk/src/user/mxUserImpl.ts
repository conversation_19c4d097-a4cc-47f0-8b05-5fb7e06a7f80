import { IMxUser, LoginOption } from './mxUser';
import { User } from '@moxo/proto';
import { IRequestPromise } from '../network/ajax';
import { sendBizServerRequest } from '../common/bizServerRequest';

export class MxUserImpl implements IMxUser {
  readonly id: string;
  readonly _user: User;

  constructor(user: User) {
    this.id = user.id;
    this._user = user;
  }
  login(opt?: LoginOption): IRequestPromise<User> {
    const body = {};
    return sendBizServerRequest(body, {}).then((response) => {
      return response?.object?.user;
    });
  }
}
